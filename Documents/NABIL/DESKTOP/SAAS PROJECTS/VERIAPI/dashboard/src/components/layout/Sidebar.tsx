'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
// import { useTranslations } from 'next-intl';
import {
  LayoutDashboard,
  FileText,
  Users,
  Settings,
  CreditCard,
  Plug,
  Building2,
  Menu,
  X,
  LogOut,
  ChevronDown,
  ChevronRight,
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/lib/utils';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  current?: boolean;
  children?: NavigationItem[];
}

interface SidebarProps {
  className?: string;
}

export default function Sidebar({ className }: SidebarProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const pathname = usePathname();
  // const t = useTranslations();
  const { logout, user } = useAuth();

  const navigation: NavigationItem[] = [
    {
      name: 'Panel',
      href: '/dashboard',
      icon: LayoutDashboard,
    },
    {
      name: 'Facturas',
      href: '/dashboard/invoices',
      icon: FileText,
    },
    {
      name: 'Clientes',
      href: '/dashboard/customers',
      icon: Users,
    },
    {
      name: 'Integraciones',
      href: '/dashboard/integrations',
      icon: Plug,
    },
    {
      name: 'Suscripción',
      href: '/dashboard/subscription',
      icon: CreditCard,
    },
    {
      name: 'Configuración',
      href: '/dashboard/settings',
      icon: Settings,
      children: [
        {
          name: 'Empresa',
          href: '/dashboard/settings/company',
          icon: Building2,
        },
        {
          name: 'Perfil',
          href: '/dashboard/settings/profile',
          icon: Users,
        },
      ],
    },
  ];

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  const isCurrentPath = (href: string) => {
    return pathname === href || pathname.startsWith(href + '/');
  };

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const isExpanded = expandedItems.includes(item.name);
    const isCurrent = isCurrentPath(item.href);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.name}>
        <div className="flex items-center">
          {hasChildren ? (
            <button
              onClick={() => toggleExpanded(item.name)}
              className={cn(
                'group flex w-full items-center rounded-xl px-3 py-2.5 text-sm font-medium transition-all duration-200',
                level > 0 ? 'pl-10' : '',
                isCurrent
                  ? 'bg-slate-100 text-slate-900'
                  : 'text-slate-600 hover:bg-slate-50 hover:text-slate-900'
              )}
            >
              <item.icon
                className={cn(
                  'mr-3 h-5 w-5 flex-shrink-0',
                  isCurrent ? 'text-slate-700' : 'text-slate-400 group-hover:text-slate-600'
                )}
              />
              <span className="flex-1 text-left">{item.name}</span>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4 text-slate-400" />
              ) : (
                <ChevronRight className="h-4 w-4 text-slate-400" />
              )}
            </button>
          ) : (
            <Link
              href={item.href}
              className={cn(
                'group flex w-full items-center rounded-xl px-3 py-2.5 text-sm font-medium transition-all duration-200',
                level > 0 ? 'pl-10' : '',
                isCurrent
                  ? 'bg-slate-100 text-slate-900'
                  : 'text-slate-600 hover:bg-slate-50 hover:text-slate-900'
              )}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <item.icon
                className={cn(
                  'mr-3 h-5 w-5 flex-shrink-0',
                  isCurrent ? 'text-slate-700' : 'text-slate-400 group-hover:text-slate-600'
                )}
              />
              {item.name}
            </Link>
          )}
        </div>

        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const sidebarContent = (
    <div className="flex h-full flex-col">
      {/* Logo */}
      <div className="flex h-16 flex-shrink-0 items-center px-6">
        <div className="flex items-center">
          <div className="h-8 w-8 rounded-xl bg-slate-900 flex items-center justify-center">
            <FileText className="h-4 w-4 text-white" />
          </div>
          <span className="ml-3 text-xl font-bold text-slate-900">VeriAPI</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-2 px-4 py-6">
        {navigation.map(item => renderNavigationItem(item))}
      </nav>

      {/* User section */}
      <div className="flex-shrink-0 border-t border-slate-200 p-4 mx-4 mb-4">
        <div className="flex items-center">
          <div className="h-9 w-9 rounded-xl bg-slate-900 flex items-center justify-center">
            <span className="text-sm font-medium text-white">
              {user?.firstName?.charAt(0) || 'U'}
            </span>
          </div>
          <div className="ml-3 flex-1">
            <p className="text-sm font-medium text-slate-900">
              {user?.fullName || 'Usuario'}
            </p>
            <p className="text-xs text-slate-500">{user?.email}</p>
          </div>
          <button
            onClick={logout}
            className="ml-3 flex-shrink-0 rounded-lg p-1.5 text-slate-400 hover:text-slate-600 hover:bg-slate-100 transition-colors"
            title="Cerrar Sesión"
          >
            <LogOut className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden">
        <button
          type="button"
          className="inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
          onClick={() => setIsMobileMenuOpen(true)}
        >
          <Menu className="h-6 w-6" />
        </button>
      </div>

      {/* Mobile menu overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setIsMobileMenuOpen(false)} />
          <div className="fixed inset-y-0 left-0 flex w-full max-w-xs flex-col bg-white">
            <div className="absolute top-0 right-0 -mr-12 pt-2">
              <button
                type="button"
                className="ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <X className="h-6 w-6 text-white" />
              </button>
            </div>
            {sidebarContent}
          </div>
        </div>
      )}

      {/* Desktop sidebar */}
      <div className={cn('hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0', className)}>
        <div className="flex flex-col flex-grow bg-white border-r border-slate-200/60 overflow-y-auto">
          {sidebarContent}
        </div>
      </div>
    </>
  );
}
