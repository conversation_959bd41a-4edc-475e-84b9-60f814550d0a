'use client'

import { useState } from 'react'
import {
  Search,
  Filter,
  Download,
  Plus,
  TrendingUp,
  TrendingDown,
  Users,
  ShoppingCart,
  DollarSign,
  Activity,
  Eye,
  MoreHorizontal,
  Calendar,
  Bell,
  Settings,
  User,
  Menu,
  X,
  Home,
  Package,
  BarChart3,
  FileText,
  Zap
} from 'lucide-react'

// Sample data matching the reference design
const salesData = [
  { date: '2 Jan', value: 120, orders: 150 },
  { date: '9 Jan', value: 140, orders: 180 },
  { date: '15 Jan', value: 124, orders: 160 },
  { date: '21 Jan', value: 160, orders: 200 },
  { date: '28 Jan', value: 150, orders: 190 },
]

const products = [
  { 
    id: 1, 
    name: 'Winter Jacket', 
    price: 45.67, 
    stock: 89, 
    sales: 33, 
    churnRisk: 'Low',
    color: '#f59e0b',
    riskLevel: 7.1
  },
  { 
    id: 2, 
    name: 'V2A Helmet', 
    price: 150.25, 
    stock: 77, 
    sales: 11, 
    churnRisk: 'Med',
    color: '#ef4444',
    riskLevel: 13
  },
  { 
    id: 3, 
    name: 'Oversized Shirt', 
    price: 300.99, 
    stock: 44, 
    sales: 93, 
    churnRisk: 'Med',
    color: '#f59e0b',
    riskLevel: 13
  },
  { 
    id: 4, 
    name: 'Bike Glove', 
    price: 75.50, 
    stock: 65, 
    sales: 15, 
    churnRisk: 'Low',
    color: '#10b981',
    riskLevel: 5.2
  },
  { 
    id: 5, 
    name: 'Short Pants', 
    price: 400.00, 
    stock: 90, 
    sales: 5, 
    churnRisk: 'High',
    color: '#ef4444',
    riskLevel: 19
  },
  { 
    id: 6, 
    name: 'Mamba Bag', 
    price: 250.00, 
    stock: 101, 
    sales: 8, 
    churnRisk: 'Low',
    color: '#10b981',
    riskLevel: 4.7
  },
  { 
    id: 7, 
    name: 'Summer Jacket', 
    price: 175.30, 
    stock: 55, 
    sales: 22, 
    churnRisk: 'Low',
    color: '#10b981',
    riskLevel: 7.1
  },
]

export default function AnalyticsDashboard() {
  const [timeFilter, setTimeFilter] = useState('Monthly')

  const getRiskBadge = (risk: string, level: number) => {
    if (risk === 'Low') {
      return <span className="status-low">{level}% Low</span>
    } else if (risk === 'Med') {
      return <span className="status-med">{level}% Med</span>
    } else {
      return <span className="status-high">{level}% High</span>
    }
  }

  const getProductIcon = (name: string) => {
    const colors = ['#f59e0b', '#ef4444', '#10b981', '#3b82f6', '#8b5cf6']
    const colorIndex = name.length % colors.length
    return (
      <div 
        className="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold"
        style={{ backgroundColor: colors[colorIndex] }}
      >
        {name.charAt(0)}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900">
      {/* Top Navigation */}
      <div className="bg-slate-800 border-b border-slate-700">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Left side - Logo and Navigation */}
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-5 h-5 text-white" />
                </div>
              </div>
              
              <nav className="flex items-center space-x-6">
                <a href="#" className="flex items-center space-x-2 text-white bg-slate-700 px-3 py-2 rounded-lg">
                  <Home className="w-4 h-4" />
                  <span className="text-sm font-medium">Home</span>
                </a>
                <a href="#" className="flex items-center space-x-2 text-slate-300 hover:text-white px-3 py-2 rounded-lg hover:bg-slate-700">
                  <Package className="w-4 h-4" />
                  <span className="text-sm font-medium">Products</span>
                </a>
                <a href="#" className="flex items-center space-x-2 text-slate-300 hover:text-white px-3 py-2 rounded-lg hover:bg-slate-700">
                  <BarChart3 className="w-4 h-4" />
                  <span className="text-sm font-medium">Analytics</span>
                </a>
                <a href="#" className="flex items-center space-x-2 text-slate-300 hover:text-white px-3 py-2 rounded-lg hover:bg-slate-700">
                  <FileText className="w-4 h-4" />
                  <span className="text-sm font-medium">Reports</span>
                </a>
              </nav>
            </div>

            {/* Right side - Search and User */}
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <Input
                  className="w-80 bg-slate-700 border-slate-600 text-white placeholder-slate-400 pl-10"
                  placeholder="Search across VeriAPI"
                />
              </div>
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white">
                <Calendar className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white relative">
                <Bell className="w-4 h-4" />
                <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </Button>
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white">
                <Settings className="w-4 h-4" />
              </Button>
              <div className="w-8 h-8 bg-slate-600 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-slate-300" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white mb-2">Hello Antoine!</h1>
              <p className="text-slate-400">Displaying the data from June 2025</p>
            </div>
            <div className="flex items-center space-x-3">
              <Button 
                variant="ghost" 
                className="text-slate-300 hover:text-white border border-slate-600"
              >
                Daily
              </Button>
              <Button 
                variant="ghost" 
                className="text-slate-300 hover:text-white border border-slate-600"
              >
                Weekly
              </Button>
              <Button 
                className="bg-slate-700 text-white hover:bg-slate-600 border border-slate-600"
              >
                Monthly
              </Button>
              <Button 
                className="bg-emerald-500 text-white hover:bg-emerald-600"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Product Batch
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Sales Card */}
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm mb-1">Total Sales</p>
                  <p className="text-2xl font-bold text-white">$ 1,284.00</p>
                  <p className="text-emerald-400 text-sm flex items-center mt-2">
                    <TrendingUp className="w-3 h-3 mr-1" />
                    11% vs prev 30 days
                  </p>
                </div>
                <div className="w-10 h-10 bg-slate-700 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-5 h-5 text-slate-300" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Total Products Card */}
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm mb-1">Total Products</p>
                  <p className="text-2xl font-bold text-white">122</p>
                  <p className="text-red-400 text-sm flex items-center mt-2">
                    <TrendingDown className="w-3 h-3 mr-1" />
                    0% vs prev 30 days
                  </p>
                </div>
                <div className="w-10 h-10 bg-slate-700 rounded-lg flex items-center justify-center">
                  <Package className="w-5 h-5 text-slate-300" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Latest Churn Card */}
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm mb-1">% Latest Churn</p>
                  <p className="text-2xl font-bold text-white">7.81%</p>
                  <p className="text-red-400 text-sm flex items-center mt-2">
                    <TrendingDown className="w-3 h-3 mr-1" />
                    1.7% vs prev 30 days
                  </p>
                </div>
                <div className="w-10 h-10 bg-slate-700 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-slate-300" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* AI Actions Card */}
          <Card className="ai-action-card border-0 text-white">
            <CardContent className="p-6 relative z-10">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/80 text-sm mb-1">8 Actions</p>
                  <p className="text-lg font-semibold text-white mb-2">Recommended with AI</p>
                  <p className="text-white/70 text-xs mb-3">Runtime update</p>
                  <Button 
                    size="sm" 
                    className="bg-white/20 text-white hover:bg-white/30 border-0"
                  >
                    See All Actions →
                  </Button>
                </div>
                <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Bottom Section - Analytics and Products */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Quick Analytics Card */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-white text-lg">Quick Analytics</CardTitle>
                <div className="flex items-center space-x-2">
                  <select className="bg-slate-700 border-slate-600 text-white text-sm rounded px-2 py-1">
                    <option>View Total Sales</option>
                  </select>
                  <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Chart Stats */}
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="bg-emerald-500/10 border border-emerald-500/20 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                    <span className="text-slate-400 text-sm">Avg Order Value</span>
                  </div>
                  <p className="text-white text-xl font-bold">$ 124.32</p>
                </div>
                <div className="text-center">
                  <p className="text-slate-400 text-sm mb-1">Last Period Sales</p>
                  <p className="text-white text-lg font-semibold">$ 901.77</p>
                </div>
                <div className="text-center">
                  <p className="text-slate-400 text-sm mb-1">Low Stock Products</p>
                  <p className="text-white text-lg font-semibold">7 items</p>
                </div>
              </div>

              {/* Chart */}
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={salesData}>
                    <defs>
                      <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis
                      dataKey="date"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#9ca3af', fontSize: 12 }}
                    />
                    <YAxis hide />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#1f2937',
                        border: '1px solid #374151',
                        borderRadius: '8px',
                        color: '#fff'
                      }}
                    />
                    <Area
                      type="monotone"
                      dataKey="value"
                      stroke="#10b981"
                      strokeWidth={2}
                      fillOpacity={1}
                      fill="url(#colorValue)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>

              {/* Bottom Stats */}
              <div className="mt-6 space-y-3">
                <div className="flex items-center justify-between py-2">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center">
                      <Package className="w-4 h-4 text-slate-300" />
                    </div>
                    <span className="text-slate-300 text-sm">6 orders to fulfill</span>
                  </div>
                  <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                    →
                  </Button>
                </div>
                <div className="flex items-center justify-between py-2">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center">
                      <DollarSign className="w-4 h-4 text-slate-300" />
                    </div>
                    <span className="text-slate-300 text-sm">50+ payments to capture</span>
                  </div>
                  <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                    →
                  </Button>
                </div>
                <div className="flex items-center justify-between py-2">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-slate-700 rounded-lg flex items-center justify-center">
                      <Activity className="w-4 h-4 text-slate-300" />
                    </div>
                    <span className="text-slate-300 text-sm">1 chargeback to review</span>
                  </div>
                  <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                    →
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Products Table */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <CardTitle className="text-white text-lg">Products</CardTitle>
                  <Badge variant="secondary" className="bg-slate-700 text-slate-300">
                    192
                  </Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-slate-400 w-3 h-3" />
                    <Input
                      className="w-32 h-8 bg-slate-700 border-slate-600 text-white placeholder-slate-400 pl-7 text-xs"
                      placeholder="Search"
                    />
                  </div>
                  <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white h-8">
                    <Filter className="w-3 h-3 mr-1" />
                    Filter
                  </Button>
                  <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                {/* Table Header */}
                <div className="grid grid-cols-6 gap-4 text-xs text-slate-400 font-medium pb-2 border-b border-slate-700">
                  <span>Name</span>
                  <span>Price</span>
                  <span>Stock</span>
                  <span>Sales</span>
                  <span>Churn Risk</span>
                  <span></span>
                </div>

                {/* Table Rows */}
                {products.map((product) => (
                  <div key={product.id} className="grid grid-cols-6 gap-4 items-center py-3 hover:bg-slate-700/50 rounded-lg px-2">
                    <div className="flex items-center space-x-3">
                      {getProductIcon(product.name)}
                      <span className="text-white text-sm font-medium">{product.name}</span>
                    </div>
                    <span className="text-slate-300 text-sm">{product.price}</span>
                    <span className="text-slate-300 text-sm">{product.stock}</span>
                    <span className="text-slate-300 text-sm">{product.sales}</span>
                    <div>
                      {getRiskBadge(product.churnRisk, product.riskLevel)}
                    </div>
                    <div className="flex justify-end">
                      <div className="flex space-x-1">
                        {[1, 2, 3].map((bar) => (
                          <div
                            key={bar}
                            className="w-1 h-4 rounded-full"
                            style={{ backgroundColor: product.color }}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
