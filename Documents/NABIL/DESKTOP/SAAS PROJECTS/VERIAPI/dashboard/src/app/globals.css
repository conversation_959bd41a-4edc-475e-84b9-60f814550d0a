@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 248 250 252; /* Very light gray background */
    --foreground: 15 23 42; /* Dark slate text */
    --card: 255 255 255; /* Pure white cards */
    --card-foreground: 15 23 42;
    --popover: 255 255 255;
    --popover-foreground: 15 23 42;
    --primary: 15 23 42; /* Dark slate primary */
    --primary-foreground: 248 250 252;
    --secondary: 241 245 249; /* Light gray secondary */
    --secondary-foreground: 15 23 42;
    --muted: 248 250 252; /* Very light muted */
    --muted-foreground: 100 116 139; /* Medium gray text */
    --accent: 241 245 249;
    --accent-foreground: 15 23 42;
    --destructive: 239 68 68; /* Red for destructive actions */
    --destructive-foreground: 248 250 252;
    --border: 226 232 240; /* Light border */
    --input: 226 232 240;
    --ring: 15 23 42;
    --radius: 0.75rem; /* Slightly more rounded */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(203 213 225);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(148 163 184);
  }

  /* Loading spinner */
  .spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-r-transparent rounded-full animate-spin;
  }

  /* Card styles - Minimal design */
  .card {
    @apply bg-card text-card-foreground rounded-xl border border-slate-200/60 shadow-sm hover:shadow-md transition-all duration-200;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6 pb-4;
  }

  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight text-slate-900;
  }

  .card-description {
    @apply text-sm text-slate-500;
  }

  .card-content {
    @apply p-6 pt-2;
  }

  .card-footer {
    @apply flex items-center p-6 pt-4;
  }

  /* Button styles - Minimal design */
  .btn {
    @apply inline-flex items-center justify-center rounded-xl text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply bg-slate-900 text-white hover:bg-slate-800 shadow-sm;
  }

  .btn-secondary {
    @apply bg-slate-100 text-slate-900 hover:bg-slate-200 border border-slate-200;
  }

  .btn-outline {
    @apply border border-slate-300 bg-transparent text-slate-700 hover:bg-slate-50 hover:text-slate-900;
  }

  .btn-ghost {
    @apply text-slate-700 hover:bg-slate-100 hover:text-slate-900;
  }

  .btn-destructive {
    @apply bg-red-500 text-white hover:bg-red-600 shadow-sm;
  }

  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-12 px-8 text-base;
  }

  /* Input styles - Minimal design */
  .input {
    @apply flex h-10 w-full rounded-xl border border-slate-300 bg-white px-3 py-2 text-sm placeholder:text-slate-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2 focus-visible:border-slate-400 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
  }

  /* Table styles */
  .table {
    @apply w-full caption-bottom text-sm;
  }

  .table-header {
    @apply border-b;
  }

  .table-row {
    @apply border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted;
  }

  .table-head {
    @apply h-12 px-4 text-left align-middle font-medium text-muted-foreground;
  }

  .table-cell {
    @apply p-4 align-middle;
  }

  /* Badge styles - Minimal design */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors;
  }

  .badge-default {
    @apply bg-slate-100 text-slate-800;
  }

  .badge-secondary {
    @apply bg-slate-50 text-slate-600 border border-slate-200;
  }

  .badge-destructive {
    @apply bg-red-100 text-red-800;
  }

  .badge-outline {
    @apply border border-slate-200 text-slate-700 bg-white;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideIn {
    from {
      transform: translateY(-10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}
