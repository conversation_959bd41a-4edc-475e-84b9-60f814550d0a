'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import {
  FileText,
  Users,
  Euro,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Calendar,
  Loader2,
  DollarSign,
  Activity,
  BarChart3,
  Settings
} from 'lucide-react';
import { formatCurrency, formatNumber } from '@/lib/utils';
import { companyApi, invoicesApi, customersApi } from '@/lib/api';
import { toast } from 'react-hot-toast';

export default function DashboardPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<any>(null);
  const [recentInvoices, setRecentInvoices] = useState<any[]>([]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch dashboard stats
        const [statsData, invoicesData, customersData] = await Promise.all([
          companyApi.getStats(),
          invoicesApi.getInvoices({ page: 1, limit: 5, sortBy: 'createdAt', sortOrder: 'desc' }),
          customersApi.getCustomers({ page: 1, limit: 1 }) // Just to get total count
        ]);

        setStats(statsData);
        setRecentInvoices(invoicesData.invoices || []);

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast.error('Error al cargar los datos del dashboard');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <DashboardLayout title="Dashboard">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Cargando dashboard...</span>
        </div>
      </DashboardLayout>
    );
  }

  if (!stats) {
    return (
      <DashboardLayout title="Dashboard">
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error al cargar datos</h3>
          <p className="text-gray-600">No se pudieron cargar las estadísticas del dashboard.</p>
        </div>
      </DashboardLayout>
    );
  }

  const statCards = [
    {
      title: 'Total Facturas',
      value: formatNumber(stats.totalInvoices || 0),
      change: '+12%',
      changeType: 'positive' as const,
      icon: FileText,
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600',
    },
    {
      title: 'Ingresos Totales',
      value: formatCurrency(stats.totalRevenue || 0),
      change: '+8.2%',
      changeType: 'positive' as const,
      icon: Euro,
      bgColor: 'bg-emerald-50',
      iconColor: 'text-emerald-600',
    },
    {
      title: 'Importe Pendiente',
      value: formatCurrency(stats.pendingAmount || 0),
      change: '-15%',
      changeType: 'negative' as const,
      icon: Clock,
      bgColor: 'bg-amber-50',
      iconColor: 'text-amber-600',
    },
    {
      title: 'Total Clientes',
      value: formatNumber(stats.totalCustomers || 0),
      change: '+3',
      changeType: 'positive' as const,
      icon: Users,
      bgColor: 'bg-violet-50',
      iconColor: 'text-violet-600',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4" />;
      case 'sent':
        return <FileText className="h-4 w-4" />;
      case 'overdue':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <DashboardLayout title="Panel de Control">
      <div className="space-y-6">
        {/* Header section matching reference image */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white mb-2">Panel de Control</h1>
            <p className="text-slate-400">Resumen de tu actividad de facturación</p>
          </div>
          <div className="flex items-center space-x-4">
            <button className="bg-slate-700 hover:bg-slate-600 text-white px-4 py-2 rounded-lg transition-colors">
              Exportar
            </button>
            <button
              onClick={() => router.push('/dashboard/invoices')}
              className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Nueva Factura
            </button>
          </div>
        </div>

        {/* Stats cards matching reference layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400 mb-1">Total Facturas</p>
                  <p className="text-2xl font-bold text-white">{formatNumber(stats.totalInvoices || 0)}</p>
                  <p className="text-xs text-emerald-400 mt-1">+12% vs mes anterior</p>
                </div>
                <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <FileText className="w-6 h-6 text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400 mb-1">Ingresos Totales</p>
                  <p className="text-2xl font-bold text-white">{formatCurrency(stats.totalRevenue || 0)}</p>
                  <p className="text-xs text-emerald-400 mt-1">+8.2% vs mes anterior</p>
                </div>
                <div className="w-12 h-12 bg-emerald-500/20 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-emerald-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400 mb-1">Pendientes</p>
                  <p className="text-2xl font-bold text-white">{formatCurrency(stats.pendingAmount || 0)}</p>
                  <p className="text-xs text-red-400 mt-1">-15% vs mes anterior</p>
                </div>
                <div className="w-12 h-12 bg-amber-500/20 rounded-lg flex items-center justify-center">
                  <Clock className="w-6 h-6 text-amber-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-400 mb-1">Total Clientes</p>
                  <p className="text-2xl font-bold text-white">{formatNumber(stats.totalCustomers || 0)}</p>
                  <p className="text-xs text-emerald-400 mt-1">+3 nuevos</p>
                </div>
                <div className="w-12 h-12 bg-violet-500/20 rounded-lg flex items-center justify-center">
                  <Users className="w-6 h-6 text-violet-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main content grid matching reference image */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left column - Recent Activity */}
          <div className="lg:col-span-2 space-y-6">
            {/* Recent Invoices Table */}
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold text-white">
                    Facturas Recientes
                  </CardTitle>
                  <button
                    onClick={() => router.push('/dashboard/invoices')}
                    className="text-emerald-400 hover:text-emerald-300 text-sm font-medium"
                  >
                    Ver todas
                  </button>
                </div>
              </CardHeader>
              <CardContent>
                {recentInvoices.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-slate-600 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-white mb-2">
                      No hay facturas recientes
                    </h3>
                    <p className="text-slate-400 mb-4">
                      Comienza creando tu primera factura
                    </p>
                    <button
                      onClick={() => router.push('/dashboard/invoices')}
                      className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors"
                    >
                      Crear Factura
                    </button>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-slate-700">
                          <th className="text-left py-3 px-4 text-sm font-medium text-slate-400">Factura</th>
                          <th className="text-left py-3 px-4 text-sm font-medium text-slate-400">Cliente</th>
                          <th className="text-left py-3 px-4 text-sm font-medium text-slate-400">Estado</th>
                          <th className="text-left py-3 px-4 text-sm font-medium text-slate-400">Importe</th>
                          <th className="text-left py-3 px-4 text-sm font-medium text-slate-400">Fecha</th>
                        </tr>
                      </thead>
                      <tbody>
                        {recentInvoices.map((invoice) => (
                          <tr
                            key={invoice.id}
                            className="border-b border-slate-700/50 hover:bg-slate-700/50 cursor-pointer transition-colors"
                            onClick={() => router.push(`/dashboard/invoices/${invoice.id}`)}
                          >
                            <td className="py-3 px-4">
                              <span className="text-white font-medium">#{invoice.number}</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-slate-300">{invoice.customer?.name || 'Sin cliente'}</span>
                            </td>
                            <td className="py-3 px-4">
                              <span
                                className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  invoice.status === 'paid' ? 'bg-emerald-500/20 text-emerald-400' :
                                  invoice.status === 'sent' ? 'bg-blue-500/20 text-blue-400' :
                                  invoice.status === 'overdue' ? 'bg-red-500/20 text-red-400' :
                                  'bg-slate-500/20 text-slate-400'
                                }`}
                              >
                                {invoice.status === 'paid' && 'Pagada'}
                                {invoice.status === 'sent' && 'Enviada'}
                                {invoice.status === 'overdue' && 'Vencida'}
                                {invoice.status === 'draft' && 'Borrador'}
                              </span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-white font-medium">{formatCurrency(invoice.total)}</span>
                            </td>
                            <td className="py-3 px-4">
                              <span className="text-slate-400">{new Date(invoice.issueDate).toLocaleDateString('es-ES')}</span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right sidebar - Quick Actions & Activity */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-white">Acciones Rápidas</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <button
                  onClick={() => router.push('/dashboard/invoices')}
                  className="w-full flex items-center space-x-3 p-3 bg-slate-700 hover:bg-slate-600 rounded-lg transition-colors"
                >
                  <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <FileText className="w-5 h-5 text-blue-400" />
                  </div>
                  <div className="text-left">
                    <p className="text-white font-medium">Nueva Factura</p>
                    <p className="text-slate-400 text-sm">Crear factura</p>
                  </div>
                </button>

                <button
                  onClick={() => router.push('/dashboard/customers')}
                  className="w-full flex items-center space-x-3 p-3 bg-slate-700 hover:bg-slate-600 rounded-lg transition-colors"
                >
                  <div className="w-10 h-10 bg-emerald-500/20 rounded-lg flex items-center justify-center">
                    <Users className="w-5 h-5 text-emerald-400" />
                  </div>
                  <div className="text-left">
                    <p className="text-white font-medium">Nuevo Cliente</p>
                    <p className="text-slate-400 text-sm">Añadir cliente</p>
                  </div>
                </button>

                <button
                  onClick={() => router.push('/dashboard/settings')}
                  className="w-full flex items-center space-x-3 p-3 bg-slate-700 hover:bg-slate-600 rounded-lg transition-colors"
                >
                  <div className="w-10 h-10 bg-violet-500/20 rounded-lg flex items-center justify-center">
                    <Settings className="w-5 h-5 text-violet-400" />
                  </div>
                  <div className="text-left">
                    <p className="text-white font-medium">Configuración</p>
                    <p className="text-slate-400 text-sm">Ajustes</p>
                  </div>
                </button>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-white">Actividad Reciente</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-emerald-500/20 rounded-full flex items-center justify-center mt-1">
                      <CheckCircle className="w-4 h-4 text-emerald-400" />
                    </div>
                    <div className="flex-1">
                      <p className="text-white text-sm">Factura #001 pagada</p>
                      <p className="text-slate-400 text-xs">Hace 2 horas</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center mt-1">
                      <FileText className="w-4 h-4 text-blue-400" />
                    </div>
                    <div className="flex-1">
                      <p className="text-white text-sm">Nueva factura creada</p>
                      <p className="text-slate-400 text-xs">Hace 4 horas</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-violet-500/20 rounded-full flex items-center justify-center mt-1">
                      <Users className="w-4 h-4 text-violet-400" />
                    </div>
                    <div className="flex-1">
                      <p className="text-white text-sm">Cliente añadido</p>
                      <p className="text-slate-400 text-xs">Ayer</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
