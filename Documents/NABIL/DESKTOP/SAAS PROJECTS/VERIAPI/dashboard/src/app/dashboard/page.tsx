'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
// import { useTranslations } from 'next-intl';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import {
  FileText,
  Users,
  Euro,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Calendar,
  Loader2
} from 'lucide-react';
import { formatCurrency, formatNumber } from '@/lib/utils';
import { companyApi, invoicesApi, customersApi } from '@/lib/api';
import { toast } from 'react-hot-toast';

export default function DashboardPage() {
  // const t = useTranslations();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<any>(null);
  const [recentInvoices, setRecentInvoices] = useState<any[]>([]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch dashboard stats
        const [statsData, invoicesData, customersData] = await Promise.all([
          companyApi.getStats(),
          invoicesApi.getInvoices({ page: 1, limit: 5, sortBy: 'createdAt', sortOrder: 'desc' }),
          customersApi.getCustomers({ page: 1, limit: 1 }) // Just to get total count
        ]);

        setStats(statsData);
        setRecentInvoices(invoicesData.invoices || []);

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast.error('Error al cargar los datos del dashboard');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <DashboardLayout title="Dashboard">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Cargando dashboard...</span>
        </div>
      </DashboardLayout>
    );
  }

  if (!stats) {
    return (
      <DashboardLayout title="Dashboard">
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error al cargar datos</h3>
          <p className="text-gray-600">No se pudieron cargar las estadísticas del dashboard.</p>
        </div>
      </DashboardLayout>
    );
  }

  const statCards = [
    {
      title: 'Total Facturas',
      value: formatNumber(stats.totalInvoices || 0),
      change: '+12%',
      changeType: 'positive' as const,
      icon: FileText,
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600',
    },
    {
      title: 'Ingresos Totales',
      value: formatCurrency(stats.totalRevenue || 0),
      change: '+8.2%',
      changeType: 'positive' as const,
      icon: Euro,
      bgColor: 'bg-emerald-50',
      iconColor: 'text-emerald-600',
    },
    {
      title: 'Importe Pendiente',
      value: formatCurrency(stats.pendingAmount || 0),
      change: '-15%',
      changeType: 'negative' as const,
      icon: Clock,
      bgColor: 'bg-amber-50',
      iconColor: 'text-amber-600',
    },
    {
      title: 'Total Clientes',
      value: formatNumber(stats.totalCustomers || 0),
      change: '+3',
      changeType: 'positive' as const,
      icon: Users,
      bgColor: 'bg-violet-50',
      iconColor: 'text-violet-600',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4" />;
      case 'sent':
        return <FileText className="h-4 w-4" />;
      case 'overdue':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <DashboardLayout title="Panel de Control">
      <div className="space-y-8">
        {/* Welcome section */}
        <div className="bg-white rounded-2xl p-8 border border-slate-100 shadow-sm">
          <h2 className="text-2xl font-bold mb-2 text-slate-900">
            ¡Bienvenido de vuelta! 👋
          </h2>
          <p className="text-slate-500">
            Aquí tienes un resumen de tu actividad de facturación
          </p>
        </div>

        {/* Stats grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statCards.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-all duration-300 border-slate-100">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-slate-500 mb-2">
                      {stat.title}
                    </p>
                    <p className="text-3xl font-bold text-slate-900 mb-3">
                      {stat.value}
                    </p>
                    <div className="flex items-center">
                      <TrendingUp className={`h-4 w-4 mr-1 ${
                        stat.changeType === 'positive' ? 'text-emerald-500' : 'text-red-500'
                      }`} />
                      <span className={`text-sm font-medium ${
                        stat.changeType === 'positive' ? 'text-emerald-600' : 'text-red-600'
                      }`}>
                        {stat.change}
                      </span>
                      <span className="text-sm text-slate-400 ml-1">vs mes anterior</span>
                    </div>
                  </div>
                  <div className={`p-4 rounded-2xl ${stat.bgColor} ml-4`}>
                    <stat.icon className={`h-7 w-7 ${stat.iconColor}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent invoices */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Facturas Recientes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentInvoices.length > 0 ? (
                  recentInvoices.map((invoice) => (
                    <div key={invoice.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-full ${getStatusColor(invoice.paymentStatus)}`}>
                          {getStatusIcon(invoice.paymentStatus)}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{invoice.invoiceNumber}</p>
                          <p className="text-sm text-gray-500">{invoice.customer?.name || 'Cliente desconocido'}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">
                          {formatCurrency(invoice.total)}
                        </p>
                        <p className="text-sm text-gray-500 flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {new Date(invoice.issueDate).toLocaleDateString('es-ES')}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">No hay facturas recientes</p>
                  </div>
                )}
              </div>
              <div className="mt-4 pt-4 border-t border-gray-200">
                <button
                  onClick={() => router.push('/dashboard/invoices')}
                  className="text-sm text-blue-600 hover:text-blue-500 font-medium transition-colors"
                >
                  Ver todas las facturas →
                </button>
              </div>
            </CardContent>
          </Card>

          {/* Monthly overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Este Mes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-4 bg-slate-50 rounded-xl border border-slate-100">
                  <div>
                    <p className="text-sm text-slate-500">Facturas creadas</p>
                    <p className="text-2xl font-bold text-slate-900">
                      {stats.totalInvoices || 0}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-slate-500">Ingresos</p>
                    <p className="text-lg font-semibold text-slate-900">
                      {formatCurrency(stats.totalRevenue || 0)}
                    </p>
                  </div>
                </div>

                <div className="flex justify-between items-center p-4 bg-emerald-50 rounded-xl border border-emerald-100">
                  <div>
                    <p className="text-sm text-slate-500">Facturas pendientes</p>
                    <p className="text-2xl font-bold text-emerald-600">
                      {stats.pendingInvoices || 0}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-slate-500">Vencidas: {stats.overdueInvoices || 0}</p>
                    <div className="w-20 bg-slate-200 rounded-full h-2 mt-1">
                      <div
                        className="bg-emerald-500 h-2 rounded-full"
                        style={{
                          width: `${stats.pendingInvoices > 0 ? ((stats.pendingInvoices - (stats.overdueInvoices || 0)) / stats.pendingInvoices) * 100 : 0}%`
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t border-slate-200">
                  <button
                    onClick={() => router.push('/dashboard/analytics')}
                    className="text-sm text-blue-600 hover:text-blue-500 font-medium transition-colors"
                  >
                    Ver estadísticas completas →
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
