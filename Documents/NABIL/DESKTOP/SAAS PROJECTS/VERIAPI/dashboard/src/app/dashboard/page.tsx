'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import {
  FileText,
  Users,
  Euro,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Calendar,
  Loader2,
  DollarSign,
  Activity,
  BarChart3
} from 'lucide-react';
import { formatCurrency, formatNumber } from '@/lib/utils';
import { companyApi, invoicesApi, customersApi } from '@/lib/api';
import { toast } from 'react-hot-toast';

export default function DashboardPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<any>(null);
  const [recentInvoices, setRecentInvoices] = useState<any[]>([]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch dashboard stats
        const [statsData, invoicesData, customersData] = await Promise.all([
          companyApi.getStats(),
          invoicesApi.getInvoices({ page: 1, limit: 5, sortBy: 'createdAt', sortOrder: 'desc' }),
          customersApi.getCustomers({ page: 1, limit: 1 }) // Just to get total count
        ]);

        setStats(statsData);
        setRecentInvoices(invoicesData.invoices || []);

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast.error('Error al cargar los datos del dashboard');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <DashboardLayout title="Dashboard">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Cargando dashboard...</span>
        </div>
      </DashboardLayout>
    );
  }

  if (!stats) {
    return (
      <DashboardLayout title="Dashboard">
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error al cargar datos</h3>
          <p className="text-gray-600">No se pudieron cargar las estadísticas del dashboard.</p>
        </div>
      </DashboardLayout>
    );
  }

  const statCards = [
    {
      title: 'Total Facturas',
      value: formatNumber(stats.totalInvoices || 0),
      change: '+12%',
      changeType: 'positive' as const,
      icon: FileText,
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600',
    },
    {
      title: 'Ingresos Totales',
      value: formatCurrency(stats.totalRevenue || 0),
      change: '+8.2%',
      changeType: 'positive' as const,
      icon: Euro,
      bgColor: 'bg-emerald-50',
      iconColor: 'text-emerald-600',
    },
    {
      title: 'Importe Pendiente',
      value: formatCurrency(stats.pendingAmount || 0),
      change: '-15%',
      changeType: 'negative' as const,
      icon: Clock,
      bgColor: 'bg-amber-50',
      iconColor: 'text-amber-600',
    },
    {
      title: 'Total Clientes',
      value: formatNumber(stats.totalCustomers || 0),
      change: '+3',
      changeType: 'positive' as const,
      icon: Users,
      bgColor: 'bg-violet-50',
      iconColor: 'text-violet-600',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'sent':
        return 'bg-blue-100 text-blue-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4" />;
      case 'sent':
        return <FileText className="h-4 w-4" />;
      case 'overdue':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <DashboardLayout title="Panel de Control">
      <div className="space-y-8">
        {/* Welcome section with dark theme */}
        <div className="bg-slate-800 rounded-2xl p-8 border border-slate-700 shadow-lg">
          <h2 className="text-2xl font-bold mb-2 text-white">
            ¡Bienvenido de vuelta! 👋
          </h2>
          <p className="text-slate-400">
            Aquí tienes un resumen de tu actividad de facturación
          </p>
        </div>

        {/* Stats grid with dark theme */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statCards.map((stat, index) => (
            <Card key={index} className="bg-slate-800 border-slate-700 hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-400 mb-2">
                      {stat.title}
                    </p>
                    <p className="text-3xl font-bold text-white mb-2">
                      {stat.value}
                    </p>
                    <div className="flex items-center">
                      <span
                        className={`text-sm font-medium ${
                          stat.changeType === 'positive'
                            ? 'text-emerald-400'
                            : 'text-red-400'
                        }`}
                      >
                        {stat.change}
                      </span>
                      <span className="text-slate-500 text-sm ml-2">
                        vs mes anterior
                      </span>
                    </div>
                  </div>
                  <div className="p-3 rounded-xl bg-slate-700">
                    <stat.icon className="h-6 w-6 text-slate-300" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent invoices with dark theme */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl font-semibold text-white">
                Facturas Recientes
              </CardTitle>
              <button
                onClick={() => router.push('/dashboard/invoices')}
                className="text-emerald-400 hover:text-emerald-300 text-sm font-medium"
              >
                Ver todas →
              </button>
            </div>
          </CardHeader>
          <CardContent>
            {recentInvoices.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-slate-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">
                  No hay facturas recientes
                </h3>
                <p className="text-slate-400 mb-4">
                  Comienza creando tu primera factura
                </p>
                <button
                  onClick={() => router.push('/dashboard/invoices')}
                  className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors"
                >
                  Crear Factura
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {recentInvoices.map((invoice) => (
                  <div
                    key={invoice.id}
                    className="flex items-center justify-between p-4 bg-slate-700 rounded-xl hover:bg-slate-600 transition-colors cursor-pointer"
                    onClick={() => router.push(`/dashboard/invoices/${invoice.id}`)}
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(invoice.status)}
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            invoice.status === 'paid' ? 'bg-emerald-500/20 text-emerald-400' :
                            invoice.status === 'sent' ? 'bg-blue-500/20 text-blue-400' :
                            invoice.status === 'overdue' ? 'bg-red-500/20 text-red-400' :
                            'bg-slate-500/20 text-slate-400'
                          }`}
                        >
                          {invoice.status === 'paid' && 'Pagada'}
                          {invoice.status === 'sent' && 'Enviada'}
                          {invoice.status === 'overdue' && 'Vencida'}
                          {invoice.status === 'draft' && 'Borrador'}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-white">
                          Factura #{invoice.number}
                        </p>
                        <p className="text-sm text-slate-400">
                          {invoice.customer?.name || 'Cliente no especificado'}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-white">
                        {formatCurrency(invoice.total)}
                      </p>
                      <p className="text-sm text-slate-400">
                        {new Date(invoice.issueDate).toLocaleDateString('es-ES')}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick actions with dark theme */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-slate-800 border-slate-700 hover:shadow-xl transition-all duration-300 cursor-pointer"
                onClick={() => router.push('/dashboard/invoices')}>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                <FileText className="h-6 w-6 text-blue-400" />
              </div>
              <h3 className="font-semibold text-white mb-2">Crear Factura</h3>
              <p className="text-sm text-slate-400">
                Genera una nueva factura para tus clientes
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700 hover:shadow-xl transition-all duration-300 cursor-pointer"
                onClick={() => router.push('/dashboard/customers')}>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-emerald-500/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Users className="h-6 w-6 text-emerald-400" />
              </div>
              <h3 className="font-semibold text-white mb-2">Gestionar Clientes</h3>
              <p className="text-sm text-slate-400">
                Administra tu base de datos de clientes
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700 hover:shadow-xl transition-all duration-300 cursor-pointer"
                onClick={() => router.push('/dashboard/settings')}>
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-violet-500/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Calendar className="h-6 w-6 text-violet-400" />
              </div>
              <h3 className="font-semibold text-white mb-2">Configuración</h3>
              <p className="text-sm text-slate-400">
                Personaliza tu empresa y preferencias
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
